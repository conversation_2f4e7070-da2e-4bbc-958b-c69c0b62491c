import { Worker } from 'bullmq';
import { exec } from 'child_process';
import Docker from 'dockerode';
import * as dotenv from 'dotenv';
import * as fs from 'fs-extra';
import { createServer } from 'http';
import * as path from 'path';
import { Duplex } from 'stream';
import { promisify } from 'util';
import { WebSocketManager } from './websocket';

// Try loading the local .env first
dotenv.config();

// If in development and local .env didn't set everything we need, try loading from root
if (process.env.NODE_ENV === 'development') {
  const rootEnvPath = path.resolve(__dirname, '../../../.env');
  if (fs.existsSync(rootEnvPath)) {
    dotenv.config({ path: rootEnvPath });
  }
}

// Add type declaration for dockerode
declare module 'dockerode';

const execPromise = promisify(exec);
const docker = new Docker();

const BUILD_IMAGE = 'react-builder';
const DEV_CONTAINER_NAME = 'react-builder-dev';
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

const SHARED_NODE_MODULES = path.join(
  __dirname,
  '..',
  'shared',
  'node_modules',
);
const SHARED_PNPM_STORE = path.join(__dirname, '..', 'shared', 'pnpm-store');

const PROJECT_TYPE = {
  NEXTJS: 'nextjs',
} as const;

type ProjectType = (typeof PROJECT_TYPE)[keyof typeof PROJECT_TYPE];

interface ProjectData {
  'package.json'?: {
    file: {
      contents: string;
    };
  };
  'next.config.js'?: {
    file: {
      contents: string;
    };
  };
  [key: string]:
    | {
        file?: {
          contents: string;
        };
        directory?: Record<string, any>;
      }
    | undefined;
}

interface BuildResult {
  success: boolean;
  logs: string;
  outputPath: string;
  projectType: ProjectType;
  error?: string;
  jobId?: string;
}

fs.ensureDirSync(SHARED_NODE_MODULES);
fs.ensureDirSync(SHARED_PNPM_STORE);

async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries = MAX_RETRIES,
): Promise<T> {
  let lastError: Error | undefined;
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries - 1) {
        await new Promise((resolve) =>
          setTimeout(resolve, RETRY_DELAY * (i + 1)),
        );
      }
    }
  }
  throw lastError || new Error('Operation failed after retries');
}

async function buildDockerImage() {
  const images = await docker.listImages();
  const imageExists = images.some((img: { RepoTags?: string[] }) =>
    img.RepoTags?.includes(`${BUILD_IMAGE}:latest`),
  );
  if (imageExists) return;

  await retryOperation(async () => {
    await execPromise(
      `docker build -t ${BUILD_IMAGE} -f ${path.join(__dirname, '..', 'Dockerfile')} .`,
    );
  });
}

async function ensureDevContainer() {
  try {
    const container = docker.getContainer(DEV_CONTAINER_NAME);
    const info = await container.inspect();
    if (info.State.Running) return container;

    await retryOperation(async () => {
      await container.start();
    });
    return container;
  } catch {
    await buildDockerImage();

    // Remove any existing container with the same name
    try {
      const existingContainer = docker.getContainer(DEV_CONTAINER_NAME);
      await existingContainer.remove({ force: true });
    } catch (err) {
      // Ignore error if container doesn't exist
    }

    // Create container with a more reliable keep-alive command
    const container = await docker.createContainer({
      Image: BUILD_IMAGE,
      name: DEV_CONTAINER_NAME,
      Cmd: ['/bin/sh', '-c', 'while true; do sleep 1; done'],
      WorkingDir: '/app',
      HostConfig: {
        Binds: [
          `${SHARED_NODE_MODULES}:/app/node_modules:rw`,
          `${SHARED_PNPM_STORE}:/root/.pnpm-store:rw`,
        ],
        Memory: 1024 * 1024 * 1024,
        CpuShares: 1024,
        RestartPolicy: {
          Name: 'on-failure',
          MaximumRetryCount: 3,
        },
      },
      Tty: true,
      OpenStdin: true,
    });

    await retryOperation(async () => {
      await container.start();
    });

    // Wait a moment to ensure container is fully started
    await new Promise((resolve) => setTimeout(resolve, 2000));

    return container;
  }
}

async function createProjectStructure(
  projectData: ProjectData,
  projectDir: string,
): Promise<ProjectType> {
  await fs.ensureDir(projectDir);

  // Create package.json
  if (projectData['package.json']?.file?.contents) {
    await fs.outputFile(
      path.join(projectDir, 'package.json'),
      projectData['package.json'].file.contents,
    );
  } else {
    await fs.outputFile(
      path.join(projectDir, 'package.json'),
      JSON.stringify(
        {
          name: 'nextjs-project',
          version: '1.0.0',
          dependencies: {
            next: '^14.0.0',
            react: '^18.2.0',
            'react-dom': '^18.2.0',
          },
          scripts: {
            dev: 'next dev',
            build: 'next build',
            start: 'next start',
            lint: 'next lint',
          },
        },
        null,
        2,
      ),
    );
  }

  if (projectData['next.config.js']?.file?.contents) {
    await fs.outputFile(
      path.join(projectDir, 'next.config.js'),
      projectData['next.config.js'].file.contents,
    );
  } else {
    const nextConfig = `/** @type {import('next').NextConfig} */\nconst nextConfig = {\n  output: 'export',\n  productionBrowserSourceMaps: false,\n  typescript: { ignoreBuildErrors: true },\n  eslint: { ignoreDuringBuilds: true },\n  images: { unoptimized: true },\n  trailingSlash: true\n};\nmodule.exports = nextConfig;`;
    await fs.outputFile(path.join(projectDir, 'next.config.js'), nextConfig);
  }

  const writeFilesAndDirs = async (
    baseDir: string,
    entries: Record<string, any>,
  ) => {
    for (const [name, data] of Object.entries(entries)) {
      if (!data) continue;
      const targetPath = path.join(baseDir, name);
      if (data.file?.contents) {
        await fs.outputFile(targetPath, data.file.contents);
      } else if (data.directory) {
        await fs.ensureDir(targetPath);
        await writeFilesAndDirs(targetPath, data.directory);
      }
    }
  };

  for (const [name, data] of Object.entries(projectData)) {
    if (name === 'package.json' || name === 'next.config.js' || !data) continue;

    const targetPath = path.join(projectDir, name);
    if (data.file?.contents) {
      await fs.outputFile(targetPath, data.file.contents);
    } else if (data.directory) {
      await fs.ensureDir(targetPath);
      await writeFilesAndDirs(targetPath, data.directory);
    }
  }

  return PROJECT_TYPE.NEXTJS;
}

async function buildInDevContainer(
  container: Docker.Container,
  projectDir: string,
  outputDir: string,
  jobId: string,
): Promise<BuildResult> {
  // Clean up the output directory first to ensure no old files remain
  await retryOperation(async () => {
    // Ensure the output directory exists but is empty
    if (await fs.pathExists(outputDir)) {
      await fs.emptyDir(outputDir);
    } else {
      await fs.ensureDir(outputDir);
    }
    console.log(`Cleaned output directory: ${outputDir}`);
  });

  await retryOperation(async () => {
    await execPromise(`docker cp ${projectDir}/. ${DEV_CONTAINER_NAME}:/app/`);
  });

  // Split the build process into separate commands to better track success/failure
  const buildCmd = `
    set -e &&
    cd /app &&
    echo "Installing dependencies..." &&
    pnpm install --prefer-offline --frozen-lockfile=false &&
    echo "Building project..." &&
    pnpm build &&
    echo "BUILD_SUCCESS" &&
    echo "Copying build output..." &&
    mkdir -p dist &&
    cp -a out/. dist/ 2>/dev/null || true &&
    cp -a public/. dist/ 2>/dev/null || true &&
    echo nextjs-static > dist/.project-type
    `;

  const exec = await container.exec({
    Cmd: ['/bin/sh', '-c', buildCmd],
    AttachStdout: true,
    AttachStderr: true,
  });

  const stream = (await exec.start({ hijack: true })) as unknown as Duplex;
  let logs = '';
  let buildSuccessDetected = false;
  let buildFailureDetected = false;

  return new Promise<BuildResult>((resolve, reject) => {
    if (!stream) {
      reject(new Error('Failed to start container execution'));
      return;
    }

    stream.on('data', (chunk: Buffer) => {
      const logChunk = chunk.toString();
      logs += logChunk;

      // Check for build success marker
      if (logChunk.includes('BUILD_SUCCESS')) {
        buildSuccessDetected = true;
      }

      // Check for common build failure patterns
      if (
        logChunk.includes('Failed to compile') ||
        logChunk.includes('Build failed') ||
        logChunk.includes('Module not found') ||
        logChunk.includes("Can't resolve") ||
        logChunk.includes('Error:') ||
        logChunk.includes('TypeError:') ||
        logChunk.includes('SyntaxError:') ||
        logChunk.includes('ReferenceError:') ||
        logChunk.includes('ELIFECYCLE') ||
        logChunk.includes('Command failed with exit code') ||
        logChunk.includes('webpack errors') ||
        logChunk.includes('Compilation failed') ||
        logChunk.includes('Build optimization failed') ||
        (logChunk.includes('next build') && logChunk.includes('failed'))
      ) {
        buildFailureDetected = true;
      }

      // Parse log messages to update status
      if (logChunk.includes('Installing dependencies')) {
        wsManager.broadcastBuildStatus({
          jobId,
          status: 'processing',
          message: 'Installing dependencies',
          progress: 60,
        });
      } else if (logChunk.includes('Building project')) {
        wsManager.broadcastBuildStatus({
          jobId,
          status: 'processing',
          message: 'Building project',
          progress: 70,
        });
      } else if (logChunk.includes('Copying build output')) {
        wsManager.broadcastBuildStatus({
          jobId,
          status: 'processing',
          message: 'Copying build output',
          progress: 90,
        });
      }
    });

    stream.on('end', async () => {
      try {
        // Check the exit code of the container execution
        const execInspect = await exec.inspect();
        const exitCode = execInspect.ExitCode;

        console.log(`Container execution finished with exit code: ${exitCode}`);
        console.log(`Build success detected: ${buildSuccessDetected}`);
        console.log(`Build failure detected: ${buildFailureDetected}`);

        // Determine if build was successful based on multiple factors
        const success =
          exitCode === 0 && buildSuccessDetected && !buildFailureDetected;

        if (success) {
          // Only copy build output if build was successful
          await retryOperation(async () => {
            // Make sure the output directory is empty before copying new content
            if (await fs.pathExists(outputDir)) {
              await fs.emptyDir(outputDir);
            } else {
              await fs.ensureDir(outputDir);
            }

            console.log(`Copying build output to: ${outputDir}`);
            await execPromise(
              `docker cp ${DEV_CONTAINER_NAME}:/app/dist/. ${outputDir}/`,
            );
          });

          // Create success marker file only after successful copy
          await fs.outputFile(path.join(outputDir, '.build-success'), 'true');
        }

        const errorMessage =
          buildFailureDetected || exitCode !== 0
            ? `Build failed with exit code ${exitCode}. Check logs for details.`
            : undefined;

        resolve({
          success,
          logs,
          outputPath: outputDir,
          projectType: PROJECT_TYPE.NEXTJS,
          error: errorMessage,
        });
      } catch (err) {
        console.error('Error in build end handler:', err);
        reject(err);
      }
    });

    stream.on('error', (err) => {
      console.error('Stream error:', err);
      reject(err);
    });
  });
}

let devContainer: Docker.Container | undefined;
let isInitializing = false;

async function initDevContainer() {
  if (isInitializing) return;
  isInitializing = true;
  try {
    devContainer = await ensureDevContainer();
  } finally {
    isInitializing = false;
  }
}

initDevContainer();

const worker = new Worker(
  'build-queue',
  async (job) => {
    const { jobId, projectData, outputDir } = job.data as {
      jobId: string;
      projectData: ProjectData;
      outputDir: string;
    };
    const projectDir = path.join(__dirname, '..', 'uploads', jobId);

    try {
      // Notify about build start
      wsManager.broadcastBuildStatus({
        jobId,
        status: 'processing',
        message: 'Initializing build environment',
        progress: 0,
      });

      await job.updateProgress(10);
      if (!devContainer) {
        await initDevContainer();
        if (!devContainer) {
          throw new Error('Failed to initialize dev container');
        }
      }
      await job.updateProgress(20);

      wsManager.broadcastBuildStatus({
        jobId,
        status: 'processing',
        message: 'Setting up project structure',
        progress: 20,
      });

      await fs.ensureDir(projectDir);
      await fs.ensureDir(outputDir);
      await fs.chmod(outputDir, 0o777);

      await createProjectStructure(projectData, projectDir);
      await job.updateProgress(50);

      wsManager.broadcastBuildStatus({
        jobId,
        status: 'processing',
        message: 'Building project in container',
        progress: 50,
      });

      const result = await buildInDevContainer(
        devContainer,
        projectDir,
        outputDir,
        jobId,
      );

      if (result.success) {
        wsManager.broadcastBuildStatus({
          jobId,
          status: 'completed',
          message: 'Build completed successfully',
          progress: 100,
        });
      } else {
        wsManager.broadcastBuildStatus({
          jobId,
          status: 'failed',
          message: 'Build failed',
          error: result.error || 'Unknown error occurred',
          logs: result.logs, // Include build logs for debugging
        });
      }

      await job.updateProgress(100);
      return result;
    } catch (err) {
      console.error(`Build failed for job ${jobId}:`, err);
      const errorMessage = err instanceof Error ? err.message : String(err);

      wsManager.broadcastBuildStatus({
        jobId,
        status: 'failed',
        message: 'Build failed',
        error: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
        logs: err instanceof Error ? err.stack || err.message : String(err),
        jobId,
      };
    }
  },
  {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
    },
    concurrency: 5,
  },
);

// Create a separate HTTP server for the worker
const workerServer = createServer();
export const wsManager = new WebSocketManager(workerServer);

// Start the worker's WebSocket server
const WORKER_PORT = process.env.WORKER_PORT || 3002;
workerServer.listen(WORKER_PORT, () => {
  console.log(
    `🚀 Worker WebSocket server running at ws://localhost:${WORKER_PORT}`,
  );
});

async function cleanup() {
  try {
    if (devContainer) {
      await devContainer.stop();
    }
    await worker.close();
    await new Promise((resolve) => workerServer.close(resolve));
  } catch (err) {
    console.error('Error during cleanup:', err);
  }
  process.exit(0);
}

process.on('SIGTERM', cleanup);
process.on('SIGINT', cleanup);
