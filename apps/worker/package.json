{"name": "@iammoderator/worker", "version": "1.0.0", "description": "Dynamic React Preview Compiler", "main": "dist/server.js", "scripts": {"build": "tsc", "start:server": "node dist/server.js", "dev:server": "cross-env NODE_ENV=development ts-node-dev --respawn src/server.ts", "start:worker": "node dist/worker.js", "dev:worker": "cross-env NODE_ENV=development ts-node-dev --respawn src/worker.ts", "dev": "concurrently \"pnpm dev:worker\" \"pnpm dev:server\"", "test": "jest", "lint": "eslint \"src/**/*.ts\" --fix"}, "dependencies": {"@iammoderator/db": "workspace:*", "axios": "^1.8.4", "bullmq": "^3.16.2", "concurrently": "^9.1.2", "cors": "^2.8.5", "dockerode": "^3.3.5", "dotenv": "^16.5.0", "esbuild": "^0.20.0", "express": "^4.21.2", "fs-extra": "^11.3.0", "glob": "^10.3.10", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/dockerode": "^3.3.38", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.12", "@types/multer": "^1.4.11", "@types/node": "^20.11.24", "@types/uuid": "^9.0.8", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}