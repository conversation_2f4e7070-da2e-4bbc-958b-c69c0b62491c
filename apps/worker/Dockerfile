FROM node:18-alpine

# Install build tools and dependencies
RUN apk add --no-cache git curl

# Install pnpm globally
RUN npm install -g pnpm@8

# Install esbuild and swc globally
RUN npm install -g esbuild @swc/cli @swc/core

# Create app directory
WORKDIR /app

# Create shared volumes for node_modules and pnpm store
VOLUME ["/app/node_modules", "/root/.pnpm-store"]

# Ensure directories exist and have correct permissions
RUN mkdir -p /app/node_modules /root/.pnpm-store && \
    chmod -R 777 /app/node_modules /root/.pnpm-store

# Create a sample package.json with vite dependencies
RUN echo '{ \
  "name": "vite-base", \
  "version": "1.0.0", \
  "dependencies": { \
    "react": "^18.2.0", \
    "react-dom": "^18.2.0" \
  }, \
  "devDependencies": { \
    "vite": "^4.0.0", \
    "@vitejs/plugin-react": "^4.0.0" \
  } \
}' > /app/package.json

# Pre-install common React and Vite dependencies to speed up builds
RUN pnpm install && \
    pnpm add -D @swc/core @swc/helpers esbuild

# Clean up cache to reduce image size
RUN pnpm store prune

# Use a simple shell as entrypoint instead of a separate script
ENTRYPOINT ["/bin/sh", "-c"]
CMD ["echo 'Container is running. Use docker exec to run commands.'"]