import react from '@vitejs/plugin-react';
import fs from 'fs';
import path from 'path';
import { defineConfig, loadEnv } from 'vite';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Check if root .env exists
  const rootEnvPath = path.resolve(__dirname, '../../.env');
  const hasRootEnv = fs.existsSync(rootEnvPath);

  // Load environment variables directly
  if (mode === 'development' && hasRootEnv) {
    // In development, load from the root .env first, then local values can override
    const rootEnv = loadEnv(mode, path.resolve(__dirname, '../..'), '');

    // Set these values directly in process.env so Vite picks them up
    Object.keys(rootEnv).forEach((key) => {
      if (!process.env[key]) {
        process.env[key] = rootEnv[key];
      }
    });
  }

  return {
    plugins: [react()],
    // Just use the current directory for Vite's built-in env loading
    // Our manual loading above will handle the root env
    server: {
      host: '0.0.0.0', // Listen on all network interfaces
      port: 5173, // Default port for Vite
      watch: {
        usePolling: true, // Enable polling for hot reload
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
  };
});
