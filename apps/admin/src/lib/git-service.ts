import { default as FS } from '@isomorphic-git/lightning-fs';
// Buffer is already made available globally by git-utils.ts,
// but importing it here ensures it's recognized if this file is processed independently.
import { Buffer } from 'buffer';

// Import all original functions and types from git-utils.ts
import {
  DEFAULT_GIT_DIR,
  clearGitWorkspaceStorage as originalClearGitWorkspaceStorage,
  createGitCommit as originalCreateGitCommit,
  ensureDirectoryExists as originalEnsureDirectoryExists,
  getAllCommits as originalGetAllCommits,
  getAllProjectFiles as originalGetAllProjectFiles,
  getChangedFilesBetweenCommits as originalGetChangedFilesBetweenCommits,
  getCommitFileCount as originalGetCommitFileCount,
  initGitRepo as originalInitGitRepo,
  initializeFs as originalInitializeFs,
  isGitRepoInitialized as originalIsGitRepoInitialized,
  pullFromGitHubRepo as originalPullFromGitHubRepo,
  pushToGitHubRepo as originalPushToGitHubRepo,
  readFile as originalReadFile,
  writeFile as originalWriteFile,
  type CreateGitCommitArgs,
  type EnsureDirectoryExistsArgs,
  type GetAllCommitsArgs,
  type GetAllProjectFilesArgs,
  type GetChangedFilesBetweenCommitsArgs,
  type GetCommitFileCountArgs,
  type InitGitRepoArgs,
  type IsGitRepoInitializedArgs,
  type PullFromGitHubRepoArgs,
  type PushToGitHubRepoArgs,
  type ReadFileArgs,
  type WriteFileArgs,
} from '../utils/git-utils';

// Ensure Buffer is available globally for isomorphic-git if not already handled by git-utils.ts
// This is generally handled in git-utils.ts, but good to be mindful.
if (typeof window !== 'undefined' && !window.Buffer) {
  window.Buffer = Buffer;
}

export function createGitService(workspaceId: string) {
  if (!workspaceId) {
    throw new Error(
      'Workspace ID is required to create a Git service instance.',
    );
  }
  // Initialize fs once for this service instance
  const fsInstance = originalInitializeFs(workspaceId);

  return {
    // Each method now uses the pre-configured fsInstance
    initGitRepo: (args?: Omit<InitGitRepoArgs, 'fs'>) =>
      originalInitGitRepo({ fs: fsInstance, ...args }),

    ensureDirectoryExists: (args: Omit<EnsureDirectoryExistsArgs, 'fs'>) =>
      originalEnsureDirectoryExists({ fs: fsInstance, ...args }),

    writeFile: (args: Omit<WriteFileArgs, 'fs'>) =>
      originalWriteFile({ fs: fsInstance, ...args }),

    readFile: (args: Omit<ReadFileArgs, 'fs'>) =>
      originalReadFile({ fs: fsInstance, ...args }),

    createGitCommit: (args: Omit<CreateGitCommitArgs, 'fs'>) =>
      originalCreateGitCommit({ fs: fsInstance, ...args }),

    getAllProjectFiles: (args?: Omit<GetAllProjectFilesArgs, 'fs'>) =>
      originalGetAllProjectFiles({ fs: fsInstance, ...args }),

    pushToGitHubRepo: (args: Omit<PushToGitHubRepoArgs, 'fs'>) =>
      originalPushToGitHubRepo({ fs: fsInstance, ...args }),

    pullFromGitHubRepo: (args: Omit<PullFromGitHubRepoArgs, 'fs'>) =>
      originalPullFromGitHubRepo({ fs: fsInstance, ...args }),

    getAllCommits: (args?: Omit<GetAllCommitsArgs, 'fs'>) =>
      originalGetAllCommits({ fs: fsInstance, ...args }),

    getCommitFileCount: (args: Omit<GetCommitFileCountArgs, 'fs'>) =>
      originalGetCommitFileCount({ fs: fsInstance, ...args }),

    getChangedFilesBetweenCommits: (
      args: Omit<GetChangedFilesBetweenCommitsArgs, 'fs'>,
    ) => originalGetChangedFilesBetweenCommits({ fs: fsInstance, ...args }),

    // Check if a Git repository is initialized
    isGitRepoInitialized: (args?: Omit<IsGitRepoInitializedArgs, 'fs'>) =>
      originalIsGitRepoInitialized({ fs: fsInstance, ...args }),

    // Expose the raw fs instance, workspaceId, and default dir if needed
    getFsInstance: (): FS => fsInstance,
    getWorkspaceId: (): string => workspaceId,
    getDefaultGitDir: (): string => DEFAULT_GIT_DIR,

    // Clear all git storage data for this workspace
    clearGitWorkspaceStorage: () =>
      originalClearGitWorkspaceStorage(workspaceId),
  };
}

// Define a type for the service instance for better TypeScript support
export type GitService = ReturnType<typeof createGitService>;
