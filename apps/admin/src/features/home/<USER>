'use client';
import DashboardHeader from '@/components/shared/DashboardHeader';
import { Button } from '@/components/ui/button';
import { useGitService } from '@/hooks/use-git-service';
import { useBaseStore } from '@/store/base-store';
import { useEffect, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router';
import { useGetRawProjectPlan } from '../../api/project-planning';
import {
  transformWorkspaceToProject,
  useGetProjects,
} from '../projects/api/get-projects';
import { useGetMe } from '../workspace/api/get-me';
import HomeContent from './components/home-content';

interface Project {
  id: string;
  title: string;
  lastEdit: string;
  seed: number;
}

export default function ProjectManagement() {
  const me = useGetMe();
  const { workspaceId, moduleId, setModuleId, setWorkspaceId } = useBaseStore(
    (state) => state,
  );
  const getRawProjectPlan = useGetRawProjectPlan(workspaceId || undefined);
  const projectBlueprint = getRawProjectPlan.data?.data;
  const navigate = useNavigate();
  const gitService = useGitService();
  const isInitializingRef = useRef(false);
  const { data: workspaces } = useGetProjects();

  const projects: Project[] = useMemo(() => {
    return workspaces?.map(transformWorkspaceToProject) || [];
  }, [workspaces]);

  useEffect(() => {
    if (projects.length > 0 && !workspaceId) {
      const firstProject = projects[0];
      setWorkspaceId(firstProject.id);
    }
  }, [projects, workspaceId, setWorkspaceId]);

  // Initialize activeModuleId based on store and blueprint.
  useEffect(() => {
    if (projectBlueprint?.modules && projectBlueprint.modules.length > 0) {
      const firstModuleId = projectBlueprint.modules[0].id;
      if (moduleId && projectBlueprint.modules.some((m) => m.id === moduleId)) {
        // moduleId is already set and valid, no action needed
      } else {
        setModuleId(firstModuleId); // Update store if defaulting or no valid selection
      }
    } else {
      setModuleId(null); // No modules, clear selection
    }
  }, [projectBlueprint, moduleId, setModuleId]);

  // Handle module selection
  const handleModuleSelect = (moduleId: string) => {
    setModuleId(moduleId);
  };

  // Automatically initialize GitHub project when workspace is loaded
  useEffect(() => {
    const initializeGitHubAndPull = async () => {
      if (workspaceId && gitService && !isInitializingRef.current) {
        try {
          // Check if git repo is already initialized using our new function
          const isGitRepoInitialized = await gitService.isGitRepoInitialized();
          const gitFiles = await gitService.getAllProjectFiles();

          if (isGitRepoInitialized && Object.keys(gitFiles)?.length > 0) {
            return; // Already initialized
          }

          isInitializingRef.current = true;

          await gitService.initGitRepo();
          await gitService.pullFromGitHubRepo({
            workspaceId,
          });

          isInitializingRef.current = false;
        } catch (error) {
          console.error('GitHub initialization/pull error:', error);
          isInitializingRef.current = false;
        }
      }
    };

    if (me.data && workspaceId) {
      initializeGitHubAndPull();
    }
  }, [gitService, me.data, workspaceId]);

  return (
    <div className="flex flex-col h-screen bg-[#0f1114] text-white">
      {/* Loading State 1: Initial Blueprint Fetch - Show skeleton */}
      {getRawProjectPlan.isLoading && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-white">Loading project blueprint...</div>
        </div>
      )}

      {/* No project blueprint - Show onboarding prompt */}
      {!getRawProjectPlan.isLoading && !projectBlueprint && (
        <div className="min-h-screen bg-[#0f1114] flex items-center justify-center px-4">
          <div className="max-w-md w-full text-center space-y-6">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-white tracking-tight">
                Build Web Apps with AI
              </h1>
              <p className="text-gray-400 text-lg">
                Transform your ideas into fully functional web applications
                using the power of artificial intelligence.
              </p>
            </div>

            <Button
              onClick={() => {
                navigate('/onboarding');
              }}
              size="lg"
              className="w-full bg-orange-500 hover:bg-orange-600 text-gray-900 font-semibold py-4 text-lg transition-colors"
            >
              Start Onboarding
            </Button>
          </div>
        </div>
      )}

      {/* Project blueprint loaded - Show main interface */}
      {!getRawProjectPlan.isLoading && projectBlueprint && (
        <>
          <DashboardHeader />
          <HomeContent
            projectBlueprint={projectBlueprint}
            activeModuleId={moduleId}
            onModuleSelect={handleModuleSelect}
            workspaceId={workspaceId}
          />
        </>
      )}
    </div>
  );
}
