import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useGitService } from '@/hooks/use-git-service';
import { useBaseStore } from '@/store/base-store';
import { ReadCommitResult } from 'isomorphic-git';
import { Check, Github } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CommitWithFileCount extends ReadCommitResult {
  fileCount?: number;
}

interface GitHubModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GitHubModal({ isOpen, onClose }: GitHubModalProps) {
  // Repository name is no longer needed as it's hardwired in backend
  const [repoUrl, setRepoUrl] = useState<string | null>(null);
  const [commits, setCommits] = useState<CommitWithFileCount[]>([]);
  const [isPushing, setIsPushing] = useState(false);
  const gitService = useGitService();
  const workspaceId = useBaseStore((state) => state.workspaceId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!gitService || !workspaceId) {
      return;
    }

    setIsPushing(true);

    try {
      // The pushToGitHubRepo function will internally:
      // 1. Get all project files
      // 2. Extract commit messages if needed
      // 3. Handle the GitHub API call with the right parameters
      const repoUrl = await gitService.pushToGitHubRepo({
        workspaceId,
        squash: true,
      });

      // Update UI on success
      if (repoUrl) {
        setRepoUrl(repoUrl);
      }
    } catch (error) {
      console.error('Failed to push to GitHub:', error);
    } finally {
      setIsPushing(false);
    }
  };

  const handleClose = () => {
    setRepoUrl(null);
    onClose();
  };

  useEffect(() => {
    const getCommitsAndFileCounts = async () => {
      if (
        isOpen &&
        gitService?.getAllCommits &&
        gitService?.getCommitFileCount
      ) {
        const fetchedCommits = await gitService.getAllCommits();

        const commitsWithCounts: CommitWithFileCount[] = await Promise.all(
          fetchedCommits.map(async (commit) => {
            const fileCount = await gitService.getCommitFileCount({
              tree: commit.commit.tree,
              oid: commit.oid,
              parent: commit.commit.parent,
            });
            return { ...commit, fileCount };
          }),
        );
        setCommits(commitsWithCounts);
      }
    };

    getCommitsAndFileCounts();
  }, [gitService, isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-[#1a1d21] border-gray-800 text-white">
        <DialogHeader>
          <DialogTitle className="text-white">Push To GitHub</DialogTitle>
        </DialogHeader>

        {repoUrl ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-green-500">
              <Check className="w-5 h-5" />
              <p className="text-sm font-medium">
                Successfully pushed to GitHub!
              </p>
            </div>

            <div className="p-3 bg-[#0f1114] rounded-md">
              <a
                href={repoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:underline text-sm flex items-center"
              >
                <Github className="w-4 h-4 mr-2" />
                {repoUrl}
              </a>
            </div>

            <div className="flex justify-end">
              <Button
                variant="secondary"
                onClick={handleClose}
                className="text-sm"
              >
                Close
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Status message */}
            {commits.length <= 1 && (
              <div className="flex items-center space-x-2 text-green-500 p-3 bg-[#0f1114]/50 rounded-md">
                <Check className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">
                  {commits.length === 1
                    ? 'Your code is up to date with the remote repository.'
                    : 'No commits available. Generate some code in the chat to create commits.'}
                </span>
              </div>
            )}

            {/* Commits list */}
            {commits.length > 1 && (
              <div>
                <h3 className="text-sm font-medium mb-2 text-gray-200">
                  Commits to push ({commits.length})
                </h3>
                <div className="bg-[#0f1114] rounded-md p-2 max-h-40 overflow-y-auto">
                  <ul className="space-y-2 text-sm">
                    {commits.map((commit, idx) => (
                      <li
                        key={`commit_${idx}`}
                        className="p-2 border-b border-gray-800 last:border-0"
                      >
                        <div className="font-medium text-gray-300">
                          {commit.commit.message}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {typeof commit.fileCount === 'number'
                            ? `${commit.fileCount} file${commit.fileCount === 1 ? '' : 's'}`
                            : 'Counting files...'}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex justify-end space-x-3 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="border-gray-700 text-gray-300 hover:bg-[#0f1114] hover:text-white text-sm"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isPushing || commits.length <= 1}
                className="bg-orange-600 hover:bg-orange-700 text-white text-sm"
              >
                {isPushing ? 'Pushing...' : 'Push to GitHub'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
