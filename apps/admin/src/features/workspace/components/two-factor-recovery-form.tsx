import { Button } from '@/components/ui/button';
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { Copy, Download } from 'lucide-react';

interface TwoFactorRecoveryModalProps {
  recoveryCodes: string[];
}

export function TwoFactorRecoveryForm({
  recoveryCodes,
}: TwoFactorRecoveryModalProps) {
  const downloadCodes = () => {
    const text = recoveryCodes.join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'recovery-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast('Recovery codes downloaded successfully');
  };

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(recoveryCodes.join('\n'));
    toast('Recovery codes copied to clipboard');
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      <DialogHeader className="text-center">
        <DialogTitle className="text-xl">Recovery Codes</DialogTitle>
        <DialogDescription>
          If you lose access to your auth app, you can login to iam moderator
          using your recovery codes. Each code can only be used once
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 w-full">
        {/* <ScrollArea className="h-[200px] rounded-md border p-4"> */}
        <div className="grid grid-cols-2 gap-2 font-mono bg-gray-200 rounded-xl p-5">
          {recoveryCodes.map((code, index) => (
            <div
              key={index}
              className="p-2 bg-gray-200 rounded-md text-center text-sm font-nunito font-bold"
            >
              {code}
            </div>
          ))}
        </div>
        {/* </ScrollArea> */}

        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={copyToClipboard}
          >
            <Copy className="mr-2 h-4 w-4" />
            Copy Codes
          </Button>
          <Button variant="default" className="flex-1" onClick={downloadCodes}>
            <Download className="mr-2 h-4 w-4" />
            Download Codes
          </Button>
        </div>
      </div>
    </div>
  );
}
