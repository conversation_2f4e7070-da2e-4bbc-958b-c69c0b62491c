version: '3.9'
services:
  core:
    build:
      context: ./ # The root of your monorepo
      dockerfile: apps/core/Dockerfile
    volumes:
      - ./apps/core:/app # Mount the core app code into the container
      - /app/node_modules # Ensure node_modules is isolated within the container
    ports:
      - '9700:9700'
    environment:
      DATABASE_URL: 'mysql://root:password@database:3306/iammodDB' # Use service name "database" instead of localhost
    depends_on:
      - database

  admin:
    build:
      context: ./ # The root of your monorepo
      dockerfile: apps/admin/Dockerfile
    volumes:
      - ./apps/admin:/app # Mount the admin app code into the container
      - /app/node_modules # Isolate node_modules inside the container
    ports:
      - '5173:5173'
    depends_on:
      - core
    environment:
      DATABASE_URL: 'mysql://root:password@database:3306/iammodDB' # Use the "database" service name

  database:
    image: mysql:8.0
    container_name: database
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: iammodDB
    ports:
      - '3306:3306'
    volumes:
      - db_data:/var/lib/mysql # Persist database data between container restarts

  redis:
    image: 'redis:alpine'
    ports:
      - '6379:6379'

volumes:
  db_data: # Define volume for persistent data
